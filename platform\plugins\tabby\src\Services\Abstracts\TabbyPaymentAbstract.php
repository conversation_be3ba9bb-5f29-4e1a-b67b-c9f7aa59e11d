<?php

namespace Bo<PERSON>ble\Tabby\Services\Abstracts;


use Bo<PERSON>ble\Tabby\Services\TabbyApiService;
use Exception;
use Illuminate\Http\Request;

abstract class TabbyPaymentAbstract
{
    protected TabbyApiService $tabbyApiService;

    public function __construct()
    {
        $this->tabbyApiService = new TabbyApiService();
    }

    /**
     * Get payment currency
     */
    public function getCurrency(): string
    {
        return config('plugins.payment.payment.currency');
    }

    /**
     * Get payment amount in the correct format for Tabby
     */
    public function getAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Check if payment method is available
     */
    public function isAvailable(): bool
    {
        return get_payment_setting('status', TABBY_PAYMENT_METHOD_NAME) == 1
            && $this->tabbyApiService->isConfigured();
    }

    /**
     * Get rejection reason message
     */
    public function getRejectionMessage(string $reason): string
    {
        $messages = [
            'not_available' => __('Sorry, <PERSON><PERSON> is unable to approve this purchase. Please use an alternative payment method for your order.'),
            'order_amount_too_high' => __('This purchase is above your current spending limit with <PERSON><PERSON>, try a smaller cart or use another payment method.'),
            'order_amount_too_low' => __('The purchase amount is below the minimum amount required to use Tabby, try adding more items or use another payment method.'),
        ];

        return $messages[$reason] ?? $messages['not_available'];
    }

    /**
     * Build payment data for Tabby API
     */
    protected function buildPaymentData(array $data): array
    {
        $amount = $this->getAmount($data['amount']);
        $currency = $data['currency'];

        // Build buyer information
        $buyer = [
            'name' => $data['customer_name'] ?? 'Guest Customer',
            'email' => $data['customer_email'] ?? '',
            'phone' => $data['customer_phone'] ?? '',
        ];

        // Add buyer history for better approval rates
        $buyerHistory = [
            'registered_since' => now()->toISOString(),
        ];

        // Add date of birth if available
        if (!empty($data['customer_dob'])) {
            $buyer['dob'] = $data['customer_dob'];
        }

        // Build shipping address
        $shippingAddress = [
            'city' => $data['shipping_city'] ?? '',
            'address' => $data['shipping_address'] ?? '',
        ];

        // Build order items
        $items = [];
        if (!empty($data['products'])) {
            foreach ($data['products'] as $product) {
                $items[] = [
                    'reference_id' => (string) ($product['id'] ?? ''),
                    'title' => $product['name'] ?? '',
                    'description' => $product['description'] ?? '',
                    'quantity' => (int) ($product['qty'] ?? 1),
                    'unit_price' => $this->getAmount($product['price'] ?? 0),
                    'discount_amount' => $this->getAmount($product['discount_amount'] ?? 0),
                    'image_url' => $product['image'] ?? '',
                    'product_url' => $product['url'] ?? '',
                    'category' => $product['category'] ?? '',
                    'brand' => $product['brand'] ?? '',
                ];
            }
        }

        // Build order information
        // Handle order_id which can be a string, integer, or array
        $orderId = $data['order_id'] ?? '';
        if (is_array($orderId)) {
            $orderId = !empty($orderId) ? (string) $orderId[0] : '';
        } else {
            $orderId = (string) $orderId;
        }

        $order = [
            'reference_id' => $orderId,
            'items' => $items,
        ];

        // Build merchant URLs
        $checkoutToken = $data['checkout_token'] ?? '';

        // Build merchant URLs - use provided URLs if available and valid
        if (!empty($checkoutToken)) {
            $merchantUrls = [
                'success' => !empty($data['return_url']) ? $data['return_url'] : route('payments.tabby.callback', ['token' => $checkoutToken]) . '?type=success',
                'cancel' => !empty($data['cancel_url']) ? $data['cancel_url'] : route('payments.tabby.callback', ['token' => $checkoutToken]) . '?type=cancel',
                'failure' => route('payments.tabby.callback', ['token' => $checkoutToken]) . '?type=failure',
            ];
        } else {
            // Fallback URLs when checkout_token is not available
            $merchantUrls = [
                'success' => $data['return_url'] ?: url('/'),
                'cancel' => $data['cancel_url'] ?: url('/'),
                'failure' => $data['cancel_url'] ?: url('/'),
            ];
        }

        // Build meta information
        $meta = [
            'order_id' => '#' . $orderId,
            'customer' => (string) ($data['customer_id'] ?? ''),
        ];

        return [
            'payment' => [
                'amount' => $amount,
                'currency' => $currency,
                'description' => $data['description'] ?? '',
                'buyer' => $buyer,
                'buyer_history' => $buyerHistory,
                'shipping_address' => $shippingAddress,
                'order' => $order,
                'meta' => $meta,
            ],
            'lang' => app()->getLocale() === 'ar' ? 'ar' : 'en',
            'merchant_code' => $this->tabbyApiService->getMerchantCode() ?: 'SA',
            'merchant_urls' => $merchantUrls,
        ];
    }

    /**
     * Execute payment
     */
    public function execute(Request $request)
    {
        try {
            return $this->makePayment($request);
        } catch (Exception $exception) {
            return false;
        }
    }

    /**
     * List currencies supported by Tabby
     */
    public function supportedCurrencyCodes(): array
    {
        return $this->tabbyApiService->getSupportedCurrencies();
    }

    abstract public function makePayment(Request $request);

    abstract public function afterMakePayment(Request $request);
}
