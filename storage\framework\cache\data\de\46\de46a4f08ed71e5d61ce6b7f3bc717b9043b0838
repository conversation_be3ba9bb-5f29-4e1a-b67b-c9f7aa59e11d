1754935894a:4:{s:13:"attributeSets";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:43:"Botble\Ecommerce\Models\ProductAttributeSet":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"ec_product_attribute_sets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:12:{s:2:"id";i:1;s:5:"title";s:5:"Color";s:4:"slug";s:5:"color";s:14:"display_layout";s:6:"visual";s:13:"is_searchable";i:1;s:13:"is_comparable";i:1;s:25:"is_use_in_product_listing";i:1;s:6:"status";s:9:"published";s:5:"order";i:0;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:32:"use_image_from_product_variation";i:0;}s:11:" * original";a:14:{s:2:"id";i:1;s:5:"title";s:5:"Color";s:4:"slug";s:5:"color";s:14:"display_layout";s:6:"visual";s:13:"is_searchable";i:1;s:13:"is_comparable";i:1;s:25:"is_use_in_product_listing";i:1;s:6:"status";s:9:"published";s:5:"order";i:0;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:32:"use_image_from_product_variation";i:0;s:16:"pivot_product_id";i:7;s:22:"pivot_attribute_set_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:5:"order";s:3:"int";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"ec_product_with_attribute_set";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:16:"attribute_set_id";i:1;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:16:"attribute_set_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:31:"Botble\Ecommerce\Models\Product":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"ec_products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:45:{s:2:"id";i:7;s:4:"name";s:15:"Audio Equipment";s:11:"description";s:332:"<ul><li> Unrestrained and portable active stereo speaker</li>
            <li> Free from the confines of wires and chords</li>
            <li> 20 hours of portable capabilities</li>
            <li> Double-ended Coil Cord with 3.5mm Stereo Plugs Included</li>
            <li> 3/4″ Dome Tweeters: 2X and 4″ Woofer: 1X</li></ul>";s:7:"content";s:1912:"<p>Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains’ signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.</p>
                                <p>- Casual unisex fit</p>

                                <p>- 64% polyester, 36% polyurethane</p>

                                <p>- Water column pressure: 4000 mm</p>

                                <p>- Model is 187cm tall and wearing a size S / M</p>

                                <p>- Unisex fit</p>

                                <p>- Drawstring hood with built-in cap</p>

                                <p>- Front placket with snap buttons</p>

                                <p>- Ventilation under armpit</p>

                                <p>- Adjustable cuffs</p>

                                <p>- Double welted front pockets</p>

                                <p>- Adjustable elastic string at hempen</p>

                                <p>- Ultrasonically welded seams</p>

                                <p>This is a unisex item, please check our clothing &amp; footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.</p>";s:6:"status";s:9:"published";s:6:"images";s:19:"["products\/7.jpg"]";s:11:"video_media";N;s:3:"sku";s:9:"SW-149-A0";s:5:"order";i:0;s:8:"quantity";i:17;s:32:"allow_checkout_when_out_of_stock";i:0;s:26:"with_storehouse_management";i:1;s:11:"is_featured";i:1;s:8:"brand_id";i:1;s:12:"is_variation";i:0;s:9:"sale_type";i:0;s:5:"price";d:592;s:10:"sale_price";N;s:10:"start_date";N;s:8:"end_date";N;s:6:"length";d:17;s:4:"wide";d:19;s:6:"height";d:16;s:6:"weight";d:690;s:6:"tax_id";N;s:5:"views";i:46134;s:10:"created_at";s:19:"2024-10-13 22:15:52";s:10:"updated_at";s:19:"2024-10-13 22:16:01";s:12:"stock_status";s:8:"in_stock";s:13:"created_by_id";i:0;s:15:"created_by_type";s:22:"Botble\ACL\Models\User";s:5:"image";N;s:12:"product_type";s:8:"physical";s:7:"barcode";N;s:13:"cost_per_item";N;s:21:"generate_license_code";i:0;s:17:"license_code_type";s:13:"auto_generate";s:22:"minimum_order_quantity";i:0;s:22:"maximum_order_quantity";i:0;s:25:"notify_attachment_updated";i:0;s:22:"specification_table_id";i:1;s:8:"store_id";i:3;s:11:"approved_by";i:0;s:13:"reviews_count";i:10;s:11:"reviews_avg";d:2.7;}s:11:" * original";a:45:{s:2:"id";i:7;s:4:"name";s:15:"Audio Equipment";s:11:"description";s:332:"<ul><li> Unrestrained and portable active stereo speaker</li>
            <li> Free from the confines of wires and chords</li>
            <li> 20 hours of portable capabilities</li>
            <li> Double-ended Coil Cord with 3.5mm Stereo Plugs Included</li>
            <li> 3/4″ Dome Tweeters: 2X and 4″ Woofer: 1X</li></ul>";s:7:"content";s:1912:"<p>Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains’ signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.</p>
                                <p>- Casual unisex fit</p>

                                <p>- 64% polyester, 36% polyurethane</p>

                                <p>- Water column pressure: 4000 mm</p>

                                <p>- Model is 187cm tall and wearing a size S / M</p>

                                <p>- Unisex fit</p>

                                <p>- Drawstring hood with built-in cap</p>

                                <p>- Front placket with snap buttons</p>

                                <p>- Ventilation under armpit</p>

                                <p>- Adjustable cuffs</p>

                                <p>- Double welted front pockets</p>

                                <p>- Adjustable elastic string at hempen</p>

                                <p>- Ultrasonically welded seams</p>

                                <p>This is a unisex item, please check our clothing &amp; footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.</p>";s:6:"status";s:9:"published";s:6:"images";s:19:"["products\/7.jpg"]";s:11:"video_media";N;s:3:"sku";s:9:"SW-149-A0";s:5:"order";i:0;s:8:"quantity";i:17;s:32:"allow_checkout_when_out_of_stock";i:0;s:26:"with_storehouse_management";i:1;s:11:"is_featured";i:1;s:8:"brand_id";i:1;s:12:"is_variation";i:0;s:9:"sale_type";i:0;s:5:"price";d:592;s:10:"sale_price";N;s:10:"start_date";N;s:8:"end_date";N;s:6:"length";d:17;s:4:"wide";d:19;s:6:"height";d:16;s:6:"weight";d:690;s:6:"tax_id";N;s:5:"views";i:46134;s:10:"created_at";s:19:"2024-10-13 22:15:52";s:10:"updated_at";s:19:"2024-10-13 22:16:01";s:12:"stock_status";s:8:"in_stock";s:13:"created_by_id";i:0;s:15:"created_by_type";s:22:"Botble\ACL\Models\User";s:5:"image";N;s:12:"product_type";s:8:"physical";s:7:"barcode";N;s:13:"cost_per_item";N;s:21:"generate_license_code";i:0;s:17:"license_code_type";s:13:"auto_generate";s:22:"minimum_order_quantity";i:0;s:22:"maximum_order_quantity";i:0;s:25:"notify_attachment_updated";i:0;s:22:"specification_table_id";i:1;s:8:"store_id";i:3;s:11:"approved_by";i:0;s:13:"reviews_count";i:10;s:11:"reviews_avg";d:2.7;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:28:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:12:"stock_status";s:38:"Botble\Ecommerce\Enums\StockStatusEnum";s:12:"product_type";s:38:"Botble\Ecommerce\Enums\ProductTypeEnum";s:5:"price";s:5:"float";s:10:"sale_price";s:5:"float";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:9:"sale_type";s:3:"int";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:22:"minimum_order_quantity";s:3:"int";s:22:"maximum_order_quantity";s:3:"int";s:11:"is_featured";s:4:"bool";s:32:"allow_checkout_when_out_of_stock";s:4:"bool";s:26:"with_storehouse_management";s:4:"bool";s:21:"generate_license_code";s:4:"bool";s:25:"notify_attachment_updated";s:4:"bool";s:11:"video_media";s:4:"json";s:6:"length";s:5:"float";s:4:"wide";s:5:"float";s:6:"height";s:5:"float";s:6:"weight";s:5:"float";s:5:"views";s:3:"int";s:8:"quantity";s:3:"int";s:5:"order";s:3:"int";s:13:"cost_per_item";s:5:"float";s:12:"is_variation";s:4:"bool";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:14:"original_price";i:1;s:16:"front_sale_price";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:8:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:51;s:3:"key";s:15:"audio-equipment";s:14:"reference_type";s:31:"Botble\Ecommerce\Models\Product";s:12:"reference_id";i:7;s:6:"prefix";s:8:"products";}s:11:" * original";a:5:{s:2:"id";i:51;s:3:"key";s:15:"audio-equipment";s:14:"reference_type";s:31:"Botble\Ecommerce\Models\Product";s:12:"reference_id";i:7;s:6:"prefix";s:8:"products";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:16:"defaultVariation";O:40:"Botble\Ecommerce\Models\ProductVariation":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_variations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:11:" * original";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:17:"productAttributes";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:40:"Botble\Ecommerce\Models\ProductAttribute":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_attributes";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:4;s:16:"attribute_set_id";i:1;s:5:"title";s:5:"Black";s:4:"slug";s:5:"black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:4;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:11:" * original";a:12:{s:2:"id";i:4;s:16:"attribute_set_id";i:1;s:5:"title";s:5:"Black";s:4:"slug";s:5:"black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:4;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:18:"pivot_variation_id";i:16;s:18:"pivot_attribute_id";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:26:"ec_product_variation_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:12:"variation_id";i:16;s:12:"attribute_id";i:4;}s:11:" * original";a:2:{s:12:"variation_id";i:16;s:12:"attribute_id";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:289;s:12:"pivotRelated";O:40:"Botble\Ecommerce\Models\ProductAttribute":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_attributes";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:5:"color";i:3;s:5:"order";i:4;s:16:"attribute_set_id";i:5;s:5:"image";i:6;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:" * foreignKey";s:12:"variation_id";s:13:" * relatedKey";s:12:"attribute_id";}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:5:"color";i:3;s:5:"order";i:4;s:16:"attribute_set_id";i:5;s:5:"image";i:6;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:40:"Botble\Ecommerce\Models\ProductAttribute":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_attributes";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:10;s:16:"attribute_set_id";i:2;s:5:"title";s:3:"XXL";s:4:"slug";s:3:"xxl";s:5:"color";N;s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:5;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:11:" * original";a:12:{s:2:"id";i:10;s:16:"attribute_set_id";i:2;s:5:"title";s:3:"XXL";s:4:"slug";s:3:"xxl";s:5:"color";N;s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:5;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:18:"pivot_variation_id";i:16;s:18:"pivot_attribute_id";i:10;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:26:"ec_product_variation_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:12:"variation_id";i:16;s:12:"attribute_id";i:10;}s:11:" * original";a:2:{s:12:"variation_id";i:16;s:12:"attribute_id";i:10;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:289;s:12:"pivotRelated";r:410;s:13:" * foreignKey";s:12:"variation_id";s:13:" * relatedKey";s:12:"attribute_id";}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:5:"color";i:3;s:5:"order";i:4;s:16:"attribute_set_id";i:5;s:5:"image";i:6;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:10:"product_id";i:1;s:23:"configurable_product_id";i:2;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:18:"productCollections";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:41:"Botble\Ecommerce\Models\ProductCollection":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:22:"ec_product_collections";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:9:{s:2:"id";i:1;s:4:"name";s:11:"New Arrival";s:4:"slug";s:11:"new-arrival";s:11:"description";N;s:5:"image";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:47";s:10:"updated_at";s:19:"2024-10-13 22:15:47";s:11:"is_featured";i:0;}s:11:" * original";a:11:{s:2:"id";i:1;s:4:"name";s:11:"New Arrival";s:4:"slug";s:11:"new-arrival";s:11:"description";N;s:5:"image";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:47";s:10:"updated_at";s:19:"2024-10-13 22:15:47";s:11:"is_featured";i:0;s:16:"pivot_product_id";i:7;s:27:"pivot_product_collection_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";N;s:8:" * table";s:30:"ec_product_collection_products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:21:"product_collection_id";i:1;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:21:"product_collection_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:31:"Botble\Ecommerce\Models\Product":35:{s:13:" * connection";N;s:8:" * table";s:11:"ec_products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:28:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:12:"stock_status";s:38:"Botble\Ecommerce\Enums\StockStatusEnum";s:12:"product_type";s:38:"Botble\Ecommerce\Enums\ProductTypeEnum";s:5:"price";s:5:"float";s:10:"sale_price";s:5:"float";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:9:"sale_type";s:3:"int";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:22:"minimum_order_quantity";s:3:"int";s:22:"maximum_order_quantity";s:3:"int";s:11:"is_featured";s:4:"bool";s:32:"allow_checkout_when_out_of_stock";s:4:"bool";s:26:"with_storehouse_management";s:4:"bool";s:21:"generate_license_code";s:4:"bool";s:25:"notify_attachment_updated";s:4:"bool";s:11:"video_media";s:4:"json";s:6:"length";s:5:"float";s:4:"wide";s:5:"float";s:6:"height";s:5:"float";s:6:"weight";s:5:"float";s:5:"views";s:3:"int";s:8:"quantity";s:3:"int";s:5:"order";s:3:"int";s:13:"cost_per_item";s:5:"float";s:12:"is_variation";s:4:"bool";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:14:"original_price";i:1;s:16:"front_sale_price";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:34:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:7:"content";i:3;s:5:"image";i:4;s:6:"images";i:5;s:11:"video_media";i:6;s:3:"sku";i:7;s:5:"order";i:8;s:8:"quantity";i:9;s:32:"allow_checkout_when_out_of_stock";i:10;s:26:"with_storehouse_management";i:11;s:11:"is_featured";i:12;s:8:"brand_id";i:13;s:12:"is_variation";i:14;s:9:"sale_type";i:15;s:5:"price";i:16;s:10:"sale_price";i:17;s:10:"start_date";i:18;s:8:"end_date";i:19;s:6:"length";i:20;s:4:"wide";i:21;s:6:"height";i:22;s:6:"weight";i:23;s:6:"tax_id";i:24;s:5:"views";i:25;s:12:"stock_status";i:26;s:7:"barcode";i:27;s:13:"cost_per_item";i:28;s:21:"generate_license_code";i:29;s:17:"license_code_type";i:30;s:22:"minimum_order_quantity";i:31;s:22:"maximum_order_quantity";i:32;s:25:"notify_attachment_updated";i:33;s:22:"specification_table_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * originalPrice";d:0;s:13:" * finalPrice";d:0;}s:12:"pivotRelated";O:41:"Botble\Ecommerce\Models\ProductCollection":33:{s:13:" * connection";N;s:8:" * table";s:22:"ec_product_collections";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"image";i:4;s:6:"status";i:5;s:11:"is_featured";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:21:"product_collection_id";}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:5:"image";i:4;s:6:"status";i:5;s:11:"is_featured";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:13:"productLabels";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:5:"store";O:31:"Botble\Marketplace\Models\Store":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"mp_stores";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:23:{s:2:"id";i:3;s:4:"name";s:10:"Young Shop";s:5:"email";s:29:"<EMAIL>";s:5:"phone";s:12:"+12259498366";s:7:"address";s:23:"195 Braun Wall Apt. 028";s:7:"country";s:2:"WF";s:5:"state";s:8:"Delaware";s:4:"city";s:10:"Ferrymouth";s:11:"customer_id";i:5;s:4:"logo";s:12:"stores/3.png";s:11:"logo_square";N;s:11:"cover_image";N;s:11:"description";s:31:"Nemo enim explicabo ut nostrum.";s:7:"content";s:126:"Consequatur iure et quam molestiae dolore earum. Aspernatur sint voluptas alias ipsa. Eveniet minus nihil ipsa aut est maxime.";s:6:"status";s:9:"published";s:18:"vendor_verified_at";N;s:10:"created_at";s:19:"2024-10-13 22:16:01";s:10:"updated_at";s:19:"2024-10-13 22:16:01";s:8:"zip_code";N;s:7:"company";N;s:6:"tax_id";N;s:16:"certificate_file";N;s:18:"government_id_file";N;}s:11:" * original";a:23:{s:2:"id";i:3;s:4:"name";s:10:"Young Shop";s:5:"email";s:29:"<EMAIL>";s:5:"phone";s:12:"+12259498366";s:7:"address";s:23:"195 Braun Wall Apt. 028";s:7:"country";s:2:"WF";s:5:"state";s:8:"Delaware";s:4:"city";s:10:"Ferrymouth";s:11:"customer_id";i:5;s:4:"logo";s:12:"stores/3.png";s:11:"logo_square";N;s:11:"cover_image";N;s:11:"description";s:31:"Nemo enim explicabo ut nostrum.";s:7:"content";s:126:"Consequatur iure et quam molestiae dolore earum. Aspernatur sint voluptas alias ipsa. Eveniet minus nihil ipsa aut est maxime.";s:6:"status";s:9:"published";s:18:"vendor_verified_at";N;s:10:"created_at";s:19:"2024-10-13 22:16:01";s:10:"updated_at";s:19:"2024-10-13 22:16:01";s:8:"zip_code";N;s:7:"company";N;s:6:"tax_id";N;s:16:"certificate_file";N;s:18:"government_id_file";N;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:40:"Botble\Marketplace\Enums\StoreStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"company";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:108;s:3:"key";s:10:"young-shop";s:14:"reference_type";s:31:"Botble\Marketplace\Models\Store";s:12:"reference_id";i:3;s:6:"prefix";s:6:"stores";}s:11:" * original";a:5:{s:2:"id";i:108;s:3:"key";s:10:"young-shop";s:14:"reference_type";s:31:"Botble\Marketplace\Models\Store";s:12:"reference_id";i:3;s:6:"prefix";s:6:"stores";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:19:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"country";i:5;s:5:"state";i:6;s:4:"city";i:7;s:11:"customer_id";i:8;s:4:"logo";i:9;s:11:"logo_square";i:10;s:11:"cover_image";i:11;s:11:"description";i:12;s:7:"content";i:13;s:6:"status";i:14;s:7:"company";i:15;s:8:"zip_code";i:16;s:16:"certificate_file";i:17;s:18:"government_id_file";i:18;s:6:"tax_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:4:"tags";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:3:{i:0;O:34:"Botble\Ecommerce\Models\ProductTag":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"ec_product_tags";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:1;s:4:"name";s:10:"Electronic";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";}s:11:" * original";a:8:{s:2:"id";i:1;s:4:"name";s:10:"Electronic";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";s:16:"pivot_product_id";i:7;s:12:"pivot_tag_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";N;s:8:" * table";s:22:"ec_product_tag_product";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:1;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:31:"Botble\Ecommerce\Models\Product":35:{s:13:" * connection";N;s:8:" * table";s:11:"ec_products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:28:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:12:"stock_status";s:38:"Botble\Ecommerce\Enums\StockStatusEnum";s:12:"product_type";s:38:"Botble\Ecommerce\Enums\ProductTypeEnum";s:5:"price";s:5:"float";s:10:"sale_price";s:5:"float";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:9:"sale_type";s:3:"int";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:22:"minimum_order_quantity";s:3:"int";s:22:"maximum_order_quantity";s:3:"int";s:11:"is_featured";s:4:"bool";s:32:"allow_checkout_when_out_of_stock";s:4:"bool";s:26:"with_storehouse_management";s:4:"bool";s:21:"generate_license_code";s:4:"bool";s:25:"notify_attachment_updated";s:4:"bool";s:11:"video_media";s:4:"json";s:6:"length";s:5:"float";s:4:"wide";s:5:"float";s:6:"height";s:5:"float";s:6:"weight";s:5:"float";s:5:"views";s:3:"int";s:8:"quantity";s:3:"int";s:5:"order";s:3:"int";s:13:"cost_per_item";s:5:"float";s:12:"is_variation";s:4:"bool";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:14:"original_price";i:1;s:16:"front_sale_price";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:34:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:7:"content";i:3;s:5:"image";i:4;s:6:"images";i:5;s:11:"video_media";i:6;s:3:"sku";i:7;s:5:"order";i:8;s:8:"quantity";i:9;s:32:"allow_checkout_when_out_of_stock";i:10;s:26:"with_storehouse_management";i:11;s:11:"is_featured";i:12;s:8:"brand_id";i:13;s:12:"is_variation";i:14;s:9:"sale_type";i:15;s:5:"price";i:16;s:10:"sale_price";i:17;s:10:"start_date";i:18;s:8:"end_date";i:19;s:6:"length";i:20;s:4:"wide";i:21;s:6:"height";i:22;s:6:"weight";i:23;s:6:"tax_id";i:24;s:5:"views";i:25;s:12:"stock_status";i:26;s:7:"barcode";i:27;s:13:"cost_per_item";i:28;s:21:"generate_license_code";i:29;s:17:"license_code_type";i:30;s:22:"minimum_order_quantity";i:31;s:22:"maximum_order_quantity";i:32;s:25:"notify_attachment_updated";i:33;s:22:"specification_table_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * originalPrice";d:0;s:13:" * finalPrice";d:0;}s:12:"pivotRelated";O:34:"Botble\Ecommerce\Models\ProductTag":33:{s:13:" * connection";N;s:8:" * table";s:15:"ec_product_tags";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:6:"tag_id";}s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:68;s:3:"key";s:10:"electronic";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:1;s:6:"prefix";s:12:"product-tags";}s:11:" * original";a:5:{s:2:"id";i:68;s:3:"key";s:10:"electronic";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:1;s:6:"prefix";s:12:"product-tags";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:34:"Botble\Ecommerce\Models\ProductTag":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"ec_product_tags";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:2;s:4:"name";s:6:"Mobile";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";}s:11:" * original";a:8:{s:2:"id";i:2;s:4:"name";s:6:"Mobile";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";s:16:"pivot_product_id";i:7;s:12:"pivot_tag_id";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";N;s:8:" * table";s:22:"ec_product_tag_product";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:2;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1081;s:12:"pivotRelated";r:1182;s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:6:"tag_id";}s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:69;s:3:"key";s:6:"mobile";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:2;s:6:"prefix";s:12:"product-tags";}s:11:" * original";a:5:{s:2:"id";i:69;s:3:"key";s:6:"mobile";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:2;s:6:"prefix";s:12:"product-tags";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:34:"Botble\Ecommerce\Models\ProductTag":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"ec_product_tags";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:4;s:4:"name";s:7:"Printer";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";}s:11:" * original";a:8:{s:2:"id";i:4;s:4:"name";s:7:"Printer";s:11:"description";N;s:6:"status";s:9:"published";s:10:"created_at";s:19:"2024-10-13 22:15:56";s:10:"updated_at";s:19:"2024-10-13 22:15:56";s:16:"pivot_product_id";i:7;s:12:"pivot_tag_id";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:3:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";N;s:8:" * table";s:22:"ec_product_tag_product";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:4;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:6:"tag_id";i:4;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:1081;s:12:"pivotRelated";r:1182;s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:6:"tag_id";}s:8:"slugable";O:23:"Botble\Slug\Models\Slug":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"slugs";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:71;s:3:"key";s:7:"printer";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:4;s:6:"prefix";s:12:"product-tags";}s:11:" * original";a:5:{s:2:"id";i:71;s:3:"key";s:7:"printer";s:14:"reference_type";s:34:"Botble\Ecommerce\Models\ProductTag";s:12:"reference_id";i:4;s:6:"prefix";s:12:"product-tags";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:3:"key";i:1;s:14:"reference_type";i:2;s:12:"reference_id";i:3;s:6:"prefix";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:7:"options";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:10:"variations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:40:"Botble\Ecommerce\Models\ProductVariation":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_variations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:11:" * original";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:10:"product_id";i:1;s:23:"configurable_product_id";i:2;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:34:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:7:"content";i:3;s:5:"image";i:4;s:6:"images";i:5;s:11:"video_media";i:6;s:3:"sku";i:7;s:5:"order";i:8;s:8:"quantity";i:9;s:32:"allow_checkout_when_out_of_stock";i:10;s:26:"with_storehouse_management";i:11;s:11:"is_featured";i:12;s:8:"brand_id";i:13;s:12:"is_variation";i:14;s:9:"sale_type";i:15;s:5:"price";i:16;s:10:"sale_price";i:17;s:10:"start_date";i:18;s:8:"end_date";i:19;s:6:"length";i:20;s:4:"wide";i:21;s:6:"height";i:22;s:6:"weight";i:23;s:6:"tax_id";i:24;s:5:"views";i:25;s:12:"stock_status";i:26;s:7:"barcode";i:27;s:13:"cost_per_item";i:28;s:21:"generate_license_code";i:29;s:17:"license_code_type";i:30;s:22:"minimum_order_quantity";i:31;s:22:"maximum_order_quantity";i:32;s:25:"notify_attachment_updated";i:33;s:22:"specification_table_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * originalPrice";d:592;s:13:" * finalPrice";d:592;}s:12:"pivotRelated";O:43:"Botble\Ecommerce\Models\ProductAttributeSet":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"ec_product_attribute_sets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:5:"order";s:3:"int";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:6:"status";i:3;s:5:"order";i:4;s:14:"display_layout";i:5;s:13:"is_searchable";i:6;s:13:"is_comparable";i:7;s:25:"is_use_in_product_listing";i:8;s:32:"use_image_from_product_variation";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:16:"attribute_set_id";}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:6:"status";i:3;s:5:"order";i:4;s:14:"display_layout";i:5;s:13:"is_searchable";i:6;s:13:"is_comparable";i:7;s:25:"is_use_in_product_listing";i:8;s:32:"use_image_from_product_variation";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:43:"Botble\Ecommerce\Models\ProductAttributeSet":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:25:"ec_product_attribute_sets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:12:{s:2:"id";i:2;s:5:"title";s:4:"Size";s:4:"slug";s:4:"size";s:14:"display_layout";s:4:"text";s:13:"is_searchable";i:1;s:13:"is_comparable";i:1;s:25:"is_use_in_product_listing";i:1;s:6:"status";s:9:"published";s:5:"order";i:1;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:32:"use_image_from_product_variation";i:0;}s:11:" * original";a:14:{s:2:"id";i:2;s:5:"title";s:4:"Size";s:4:"slug";s:4:"size";s:14:"display_layout";s:4:"text";s:13:"is_searchable";i:1;s:13:"is_comparable";i:1;s:25:"is_use_in_product_listing";i:1;s:6:"status";s:9:"published";s:5:"order";i:1;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";s:32:"use_image_from_product_variation";i:0;s:16:"pivot_product_id";i:7;s:22:"pivot_attribute_set_id";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:2:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:5:"order";s:3:"int";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:29:"ec_product_with_attribute_set";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:10:"product_id";i:7;s:16:"attribute_set_id";i:2;}s:11:" * original";a:2:{s:10:"product_id";i:7;s:16:"attribute_set_id";i:2;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:95;s:12:"pivotRelated";r:1678;s:13:" * foreignKey";s:10:"product_id";s:13:" * relatedKey";s:16:"attribute_set_id";}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:6:"status";i:3;s:5:"order";i:4;s:14:"display_layout";i:5;s:13:"is_searchable";i:6;s:13:"is_comparable";i:7;s:25:"is_use_in_product_listing";i:8;s:32:"use_image_from_product_variation";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:10:"attributes";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:40:"Botble\Ecommerce\Models\ProductAttribute":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_attributes";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:4;s:16:"attribute_set_id";i:1;s:5:"title";s:5:"Black";s:4:"slug";s:5:"black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:4;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:11:" * original";a:10:{s:2:"id";i:4;s:16:"attribute_set_id";i:1;s:5:"title";s:5:"Black";s:4:"slug";s:5:"black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:4;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:5:"color";i:3;s:5:"order";i:4;s:16:"attribute_set_id";i:5;s:5:"image";i:6;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:40:"Botble\Ecommerce\Models\ProductAttribute":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_attributes";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:10;s:16:"attribute_set_id";i:2;s:5:"title";s:3:"XXL";s:4:"slug";s:3:"xxl";s:5:"color";N;s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:5;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:11:" * original";a:10:{s:2:"id";i:10;s:16:"attribute_set_id";i:2;s:5:"title";s:3:"XXL";s:4:"slug";s:3:"xxl";s:5:"color";N;s:5:"image";N;s:10:"is_default";i:0;s:5:"order";i:5;s:10:"created_at";s:19:"2024-10-13 22:15:53";s:10:"updated_at";s:19:"2024-10-13 22:15:53";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:5:"title";i:1;s:4:"slug";i:2;s:5:"color";i:3;s:5:"order";i:4;s:16:"attribute_set_id";i:5;s:5:"image";i:6;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:17:"productVariations";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:40:"Botble\Ecommerce\Models\ProductVariation":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:21:"ec_product_variations";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:11:" * original";a:4:{s:2:"id";i:16;s:10:"product_id";i:39;s:23:"configurable_product_id";i:7;s:10:"is_default";i:1;}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:7:"product";O:31:"Botble\Ecommerce\Models\Product":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"ec_products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:39;s:12:"stock_status";s:8:"in_stock";s:8:"quantity";i:17;s:26:"with_storehouse_management";i:1;s:32:"allow_checkout_when_out_of_stock";i:0;s:6:"images";s:19:"["products\/7.jpg"]";}s:11:" * original";a:6:{s:2:"id";i:39;s:12:"stock_status";s:8:"in_stock";s:8:"quantity";i:17;s:26:"with_storehouse_management";i:1;s:32:"allow_checkout_when_out_of_stock";i:0;s:6:"images";s:19:"["products\/7.jpg"]";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:28:{s:6:"status";s:32:"Botble\Base\Enums\BaseStatusEnum";s:12:"stock_status";s:38:"Botble\Ecommerce\Enums\StockStatusEnum";s:12:"product_type";s:38:"Botble\Ecommerce\Enums\ProductTypeEnum";s:5:"price";s:5:"float";s:10:"sale_price";s:5:"float";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:11:"description";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:9:"sale_type";s:3:"int";s:10:"start_date";s:8:"datetime";s:8:"end_date";s:8:"datetime";s:22:"minimum_order_quantity";s:3:"int";s:22:"maximum_order_quantity";s:3:"int";s:11:"is_featured";s:4:"bool";s:32:"allow_checkout_when_out_of_stock";s:4:"bool";s:26:"with_storehouse_management";s:4:"bool";s:21:"generate_license_code";s:4:"bool";s:25:"notify_attachment_updated";s:4:"bool";s:11:"video_media";s:4:"json";s:6:"length";s:5:"float";s:4:"wide";s:5:"float";s:6:"height";s:5:"float";s:6:"weight";s:5:"float";s:5:"views";s:3:"int";s:8:"quantity";s:3:"int";s:5:"order";s:3:"int";s:13:"cost_per_item";s:5:"float";s:12:"is_variation";s:4:"bool";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:2:{i:0;s:14:"original_price";i:1;s:16:"front_sale_price";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:34:{i:0;s:4:"name";i:1;s:11:"description";i:2;s:7:"content";i:3;s:5:"image";i:4;s:6:"images";i:5;s:11:"video_media";i:6;s:3:"sku";i:7;s:5:"order";i:8;s:8:"quantity";i:9;s:32:"allow_checkout_when_out_of_stock";i:10;s:26:"with_storehouse_management";i:11;s:11:"is_featured";i:12;s:8:"brand_id";i:13;s:12:"is_variation";i:14;s:9:"sale_type";i:15;s:5:"price";i:16;s:10:"sale_price";i:17;s:10:"start_date";i:18;s:8:"end_date";i:19;s:6:"length";i:20;s:4:"wide";i:21;s:6:"height";i:22;s:6:"weight";i:23;s:6:"tax_id";i:24;s:5:"views";i:25;s:12:"stock_status";i:26;s:7:"barcode";i:27;s:13:"cost_per_item";i:28;s:21:"generate_license_code";i:29;s:17:"license_code_type";i:30;s:22:"minimum_order_quantity";i:31;s:22:"maximum_order_quantity";i:32;s:25:"notify_attachment_updated";i:33;s:22:"specification_table_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * originalPrice";d:0;s:13:" * finalPrice";d:0;}}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:10:"product_id";i:1;s:23:"configurable_product_id";i:2;s:10:"is_default";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:21:"productVariationsInfo";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:2:{i:0;O:44:"Botble\Ecommerce\Models\ProductVariationItem":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:26:"ec_product_variation_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:12:"variation_id";i:16;s:2:"id";i:4;s:4:"slug";s:5:"black";s:5:"title";s:5:"Black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:16:"attribute_set_id";i:1;s:5:"order";i:4;s:19:"attribute_set_title";s:5:"Color";s:18:"attribute_set_slug";s:5:"color";}s:11:" * original";a:10:{s:12:"variation_id";i:16;s:2:"id";i:4;s:4:"slug";s:5:"black";s:5:"title";s:5:"Black";s:5:"color";s:7:"#2F366C";s:5:"image";N;s:16:"attribute_set_id";i:1;s:5:"order";i:4;s:19:"attribute_set_title";s:5:"Color";s:18:"attribute_set_slug";s:5:"color";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:12:"attribute_id";i:1;s:12:"variation_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:44:"Botble\Ecommerce\Models\ProductVariationItem":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:26:"ec_product_variation_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:12:"variation_id";i:16;s:2:"id";i:10;s:4:"slug";s:3:"xxl";s:5:"title";s:3:"XXL";s:5:"color";N;s:5:"image";N;s:16:"attribute_set_id";i:2;s:5:"order";i:5;s:19:"attribute_set_title";s:4:"Size";s:18:"attribute_set_slug";s:4:"size";}s:11:" * original";a:10:{s:12:"variation_id";i:16;s:2:"id";i:10;s:4:"slug";s:3:"xxl";s:5:"title";s:3:"XXL";s:5:"color";N;s:5:"image";N;s:16:"attribute_set_id";i:2;s:5:"order";i:5;s:19:"attribute_set_title";s:4:"Size";s:18:"attribute_set_slug";s:4:"size";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:12:"attribute_id";i:1;s:12:"variation_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}}