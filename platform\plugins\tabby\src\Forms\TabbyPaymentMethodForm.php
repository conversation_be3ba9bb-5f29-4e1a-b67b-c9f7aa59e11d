<?php

namespace Bo<PERSON><PERSON>\Tabby\Forms;

use <PERSON><PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Forms\FieldOptions\HtmlFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\HtmlField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Forms\PaymentMethodForm;
use Botble\Tabby\Services\TabbyWebhookService;

class TabbyPaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(TABBY_PAYMENT_METHOD_NAME)
            ->paymentName('Tabby Pay-in-4')
            ->paymentDescription(__('Customer can buy products and pay in 4 installments with Tabby. No interest, no fees.'))
            ->paymentLogo(url('vendor/core/plugins/tabby/images/tabby.svg'))
            ->paymentUrl('https://tabby.ai')
            ->paymentInstructions(view('plugins/tabby::instructions')->render())
            ->add(
                sprintf('payment_%s_public_key', TABBY_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Public Key'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('public_key', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby public API key'))
            )
            ->add(
                sprintf('payment_%s_secret_key', TABBY_PAYMENT_METHOD_NAME),
                'password',
                TextFieldOption::make()
                    ->label(__('Secret Key'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('secret_key', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby secret API key'))
            )
            ->add(
                sprintf('payment_%s_merchant_code', TABBY_PAYMENT_METHOD_NAME),
                TextField::class,
                TextFieldOption::make()
                    ->label(__('Merchant Code'))
                    ->value(BaseHelper::hasDemoModeEnabled() ? '*******************************' : get_payment_setting('merchant_code', TABBY_PAYMENT_METHOD_NAME))
                    ->helperText(__('Your Tabby merchant code'))
            )
            ->add(
                'payment_' . TABBY_PAYMENT_METHOD_NAME . '_environment',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(__('Environment'))
                    ->choices([
                        'sandbox' => __('Sandbox (Test)'),
                        'live' => __('Live (Production)'),
                    ])
                    ->selected(get_payment_setting('environment', TABBY_PAYMENT_METHOD_NAME, 'sandbox'))
                    ->helperText(__('Select the environment for Tabby payments'))
            )
            ->add(
                'webhook_management',
                HtmlField::class,
                HtmlFieldOption::make()
                    ->content($this->getWebhookManagementHtml())
            );
    }

    protected function getWebhookManagementHtml(): string
    {
        $webhookService = new TabbyWebhookService();
        $webhookStatus = $webhookService->getWebhookStatus();

        $html = '<div class="form-group mb-3">';
        $html .= '<label class="text-title-field">' . __('Webhook Management') . '</label>';
        $html .= '<div class="ui-select-wrapper">';

        if ($webhookStatus['exists']) {
            $html .= '<div class="alert alert-success">';
            $html .= '<i class="fas fa-check-circle"></i> ';
            $html .= __('Webhook is configured and active');
            $html .= '<br><small>ID: ' . ($webhookStatus['webhook_id'] ?? 'N/A') . '</small>';
            $html .= '<br><small>URL: ' . ($webhookStatus['webhook_url'] ?? 'N/A') . '</small>';
            $html .= '</div>';

            $html .= '<button type="button" class="btn btn-warning btn-sm" onclick="updateTabbyWebhook()">';
            $html .= '<i class="fas fa-sync"></i> ' . __('Update Webhook');
            $html .= '</button> ';

            $html .= '<button type="button" class="btn btn-danger btn-sm" onclick="removeTabbyWebhook()">';
            $html .= '<i class="fas fa-trash"></i> ' . __('Remove Webhook');
            $html .= '</button>';
        } else {
            $html .= '<div class="alert alert-warning">';
            $html .= '<i class="fas fa-exclamation-triangle"></i> ';
            $html .= __('No webhook configured. Webhook is required for production.');
            $html .= '</div>';

            $html .= '<button type="button" class="btn btn-primary btn-sm" onclick="setupTabbyWebhook()">';
            $html .= '<i class="fas fa-plus"></i> ' . __('Setup Webhook');
            $html .= '</button>';
        }

        $html .= '</div>';
        $html .= '<small class="form-text text-muted">';
        $html .= __('Webhooks are used to receive real-time payment status updates from Tabby.');
        $html .= '</small>';
        $html .= '</div>';

        // Add JavaScript for webhook management
        $html .= $this->getWebhookManagementScript();

        return $html;
    }

    protected function getWebhookManagementScript(): string
    {
        return '
        <script>
        function setupTabbyWebhook() {
            if (confirm("' . __('Are you sure you want to setup webhook?') . '")) {
                fetch("' . route('payments.tabby.webhook.setup') . '", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector(\'meta[name="csrf-token"]\').getAttribute("content")
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("' . __('Webhook setup successful!') . '");
                        location.reload();
                    } else {
                        alert("' . __('Webhook setup failed:') . ' " + data.message);
                    }
                })
                .catch(error => {
                    alert("' . __('Error:') . ' " + error.message);
                });
            }
        }

        function updateTabbyWebhook() {
            if (confirm("' . __('Are you sure you want to update webhook?') . '")) {
                setupTabbyWebhook(); // Same as setup for update
            }
        }

        function removeTabbyWebhook() {
            if (confirm("' . __('Are you sure you want to remove webhook?') . '")) {
                fetch("' . route('payments.tabby.webhook.remove') . '", {
                    method: "DELETE",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector(\'meta[name="csrf-token"]\').getAttribute("content")
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("' . __('Webhook removed successfully!') . '");
                        location.reload();
                    } else {
                        alert("' . __('Webhook removal failed:') . ' " + data.message);
                    }
                })
                .catch(error => {
                    alert("' . __('Error:') . ' " + error.message);
                });
            }
        }
        </script>';
    }
}
