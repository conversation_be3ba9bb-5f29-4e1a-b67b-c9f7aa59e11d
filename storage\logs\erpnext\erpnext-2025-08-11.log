[2025-08-11 18:16:31] production.INFO: Processing order for ERPNext integration {"order_id":48,"customer_email":"<EMAIL>","order_amount":"651.20"} 
[2025-08-11 18:16:31] production.INFO: ERPNext API Request {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Customer","data":{"filters":"[[\"email_id\",\"=\",\"<EMAIL>\"]]"},"headers":[]} 
[2025-08-11 18:16:33] production.INFO: ERPNext API Response {"method":"GET","url":"https://deverp.tweetechnology.com/api/resource/Customer","status_code":200,"response":{"data":[]}} 
[2025-08-11 18:16:33] production.ERROR: ERPNext Customer Operation Failed {"operation":"get_by_email","customer_email":"<EMAIL>","customer_id":null,"error":"Customer not found"} 
[2025-08-11 18:16:33] production.INFO: Customer not found, creating new customer {"order_id":48,"customer_email":"<EMAIL>"} 
[2025-08-11 18:16:33] production.INFO: ERPNext API Request {"method":"POST","url":"https://deverp.tweetechnology.com/api/resource/Customer","data":{"customer_name":"Ahmed","customer_type":"Individual","customer_group":"Commercial","territory":"All Territories","email_id":"<EMAIL>","mobile_no":"+************"},"headers":[]} 
[2025-08-11 18:16:34] production.INFO: ERPNext API Response {"method":"POST","url":"https://deverp.tweetechnology.com/api/resource/Customer","status_code":200,"response":{"data":{"name":"Ahmed","owner":"<EMAIL>","creation":"2025-08-11 23:16:34.392183","modified":"2025-08-11 23:16:34.997462","modified_by":"<EMAIL>","docstatus":0,"idx":0,"naming_series":"CUST-.YYYY.-","customer_name":"Ahmed","customer_type":"Individual","customer_group":"Commercial","territory":"All Territories","is_internal_customer":0,"language":"en","customer_primary_contact":"Ahmed-Ahmed","mobile_no":"+************","email_id":"<EMAIL>","default_commission_rate":0.0,"so_required":0,"dn_required":0,"is_frozen":0,"disabled":0,"doctype":"Customer","portal_users":[],"sales_team":[],"credit_limits":[],"accounts":[],"companies":[]}}} 
[2025-08-11 18:16:34] production.INFO: ERPNext Customer Operation Success {"operation":"create","customer_email":"<EMAIL>","customer_id":"Ahmed"} 
[2025-08-11 18:16:34] production.INFO: Creating sales order {"order_id":48,"customer_id":"Ahmed","items_count":1} 
[2025-08-11 18:16:34] production.INFO: ERPNext API Request {"method":"POST","url":"https://deverp.tweetechnology.com/api/resource/Sales Order","data":{"customer":"Ahmed","transaction_date":"2025-08-11","grand_total":651.2,"delivery_date":"2025-08-16","items":[{"item_code":"SW-149-A0","qty":1,"rate":"592.00","warehouse":"All Warehouses - TWD"}],"docstatus":1},"headers":[]} 
[2025-08-11 18:16:35] production.INFO: ERPNext API Response {"method":"POST","url":"https://deverp.tweetechnology.com/api/resource/Sales Order","status_code":404,"response":{"exc_type":"DoesNotExistError","_server_messages":"[\"{\\\"message\\\": \\\"Item SW-149-A0 not found\\\", \\\"title\\\": \\\"Message\\\", \\\"indicator\\\": \\\"red\\\", \\\"raise_exception\\\": 1, \\\"__frappe_exc_id\\\": \\\"d789922dffcb1110843ec02d499969bfe37eea0fe1e4deb88d6cf10b\\\"}\"]"}} 
[2025-08-11 18:16:35] production.ERROR: ERPNext Sales Order Operation Failed {"operation":"create","order_id":48,"customer_id":"Ahmed","sales_order_id":null,"error":"{\"exc_type\":\"DoesNotExistError\",\"_server_messages\":\"[\\\"{\\\\\\\"message\\\\\\\": \\\\\\\"Item SW-149-A0 not found\\\\\\\", \\\\\\\"title\\\\\\\": \\\\\\\"Message\\\\\\\", \\\\\\\"indicator\\\\\\\": \\\\\\\"red\\\\\\\", \\\\\\\"raise_exception\\\\\\\": 1, \\\\\\\"__frappe_exc_id\\\\\\\": \\\\\\\"d789922dffcb1110843ec02d499969bfe37eea0fe1e4deb88d6cf10b\\\\\\\"}\\\"]\"}"} 
[2025-08-11 18:16:35] production.ERROR: Failed to create sales order {"order_id":48,"customer_id":"Ahmed","error":"{\"exc_type\":\"DoesNotExistError\",\"_server_messages\":\"[\\\"{\\\\\\\"message\\\\\\\": \\\\\\\"Item SW-149-A0 not found\\\\\\\", \\\\\\\"title\\\\\\\": \\\\\\\"Message\\\\\\\", \\\\\\\"indicator\\\\\\\": \\\\\\\"red\\\\\\\", \\\\\\\"raise_exception\\\\\\\": 1, \\\\\\\"__frappe_exc_id\\\\\\\": \\\\\\\"d789922dffcb1110843ec02d499969bfe37eea0fe1e4deb88d6cf10b\\\\\\\"}\\\"]\"}"} 
