[2025-08-11 17:52:00] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:52:02] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:52:11] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:52:13] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:52:17] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:42] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:44] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:46] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:48] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:51] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 17:53:54] production.ERROR: Tabby URL generation failed: Undefined array key "return_url"  
[2025-08-11 18:10:36] production.ERROR: Client error: `POST https://api.tabby.ai/api/v2/payments/990317be-072e-4bf9-a4c6-ade3181183df/captures` resulted in a `400 Bad Request` response:
{"status":"error","errorType":"bad_data","error":"could not capture not authorized payments"}

 - D:\laragon\www\martfury\vendor\guzzlehttp\guzzle\src\Exception\RequestException.php:111  
[2025-08-11 18:29:49] production.ERROR: Tabby webhook setup failed {"error":"Client error: `POST https://api.tabby.ai/api/v1/webhooks` resulted in a `400 Bad Request` response:
{\"error\":\"invalid webhook url\",\"errorType\":\"bad_data\",\"status\":\"error\"}
"} 
[2025-08-11 18:30:03] production.ERROR: Tabby webhook setup failed {"error":"Client error: `POST https://api.tabby.ai/api/v1/webhooks` resulted in a `400 Bad Request` response:
{\"error\":\"invalid webhook url\",\"errorType\":\"bad_data\",\"status\":\"error\"}
"} 
